# ===========================================
# Docker Compose Environment Configuration
# ===========================================
# Copy this file to .env and adjust values as needed

# Application
APP_NAME="Swinx Application"
APP_ENV=local
APP_KEY=base64:your-app-key-here
APP_DEBUG=true
APP_TIMEZONE=Asia/Ho_Chi_Minh
APP_URL=http://localhost:8080

# Database Configuration (Docker)
DB_CONNECTION=mysql
DB_HOST=db
DB_PORT=3306
DB_DATABASE=swinburne
DB_USERNAME=swinx_user
DB_PASSWORD=swinx_password

# Redis Configuration (Docker)
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

# Cache & Session
CACHE_DRIVER=redis
CACHE_PREFIX=swinx_cache
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

# Queue
QUEUE_CONNECTION=redis
QUEUE_FAILED_DRIVER=database-uuids

# Logging
LOG_CHANNEL=stderr
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

# Broadcasting
BROADCAST_CONNECTION=log
PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

# Mail
MAIL_MAILER=log
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Filesystem
FILESYSTEM_DISK=local

# Development Only
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=true

# ===========================================
# Production Environment Variables
# ===========================================
# For production, set these in your deployment environment

# Production Database (example)
# DB_HOST=your-production-db-host
# DB_DATABASE=your-production-db-name
# DB_USERNAME=your-production-db-user
# DB_PASSWORD=your-secure-production-password

# Production Redis (example)
# REDIS_HOST=your-production-redis-host
# REDIS_PASSWORD=your-redis-password

# Production Settings
# APP_ENV=production
# APP_DEBUG=false
# LOG_CHANNEL=stack
# CACHE_DRIVER=redis
# SESSION_DRIVER=database
# QUEUE_CONNECTION=redis
