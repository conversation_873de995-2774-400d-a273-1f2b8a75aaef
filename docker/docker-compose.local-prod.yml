# ===========================================
# Docker Compose Local Production Override
# ===========================================
# For testing production FrankenPHP configuration locally

services:
  # Laravel Application with FrankenPHP (Local Production)
  app:
    build:
      context: .
      dockerfile: Dockerfile.production
    container_name: swinx-app-local-prod
    ports:
      - '80:80'      # HTTP
      - '443:443'    # HTTPS
      - '443:443/udp' # HTTP/3
    env_file:
      - .env.docker.production
    environment:
      - DB_HOST=db
      - APP_ENV=production
      - FRANKENPHP_NUM_THREADS=auto
      - SERVER_NAME=swinx.test
      - ACME_EMAIL=<EMAIL>
      # Override for local production testing
      - APP_DEBUG=false
      - LOG_LEVEL=info
    volumes:
      - ./storage:/app/storage
      - ./bootstrap/cache:/app/bootstrap/cache
      # FrankenPHP/Caddy volumes for certificates and configuration
      - caddy_data_local_prod:/data
      - caddy_config_local_prod:/config
      # Log directory for local production
      - ./logs:/var/log/caddy
      # Override Caddyfile for local production
      - ./Caddyfile.local-prod:/etc/caddy/Caddyfile:ro
    depends_on:
      db:
        condition: service_healthy
    networks:
      - swinx-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost/up"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # MySQL Database (Production configuration)
  db:
    image: mysql:8.0
    container_name: swinx-db-local-prod
    ports:
      - '3306:3306'
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-RootProd2024!@#SecurePass}
      MYSQL_DATABASE: ${DB_DATABASE:-swinburne}
      MYSQL_USER: ${DB_USERNAME:-swinx_prod_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-SwinxProd2024!@#SecurePass}
    volumes:
      - mysql_data_local_prod:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
      - ./backups:/backups
    networks:
      - swinx-network
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      timeout: 20s
      retries: 10
      interval: 10s
      start_period: 40s



volumes:
  mysql_data_local_prod:
    driver: local
  # FrankenPHP/Caddy volumes for local production certificates and configuration
  caddy_data_local_prod:
    driver: local
  caddy_config_local_prod:
    driver: local

networks:
  swinx-network:
    driver: bridge
