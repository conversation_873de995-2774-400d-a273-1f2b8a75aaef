# ===========================================
# FrankenPHP Production Configuration
# ===========================================
# HTTPS with automatic Let's Encrypt certificates

{
	# Global FrankenPHP configuration for production
	frankenphp {
		# Optimize for production workload
		num_threads {env.FRANKENPHP_NUM_THREADS}
		# Production worker mode for better performance
		# worker {
		#     file /app/public/index.php
		#     num {env.FRANKENPHP_WORKER_NUM}
		# }
	}
	
	# Production logging
	log {
		level WARN
		output file /var/log/caddy/access.log {
			roll_size 100mb
			roll_keep 5
			roll_keep_for 720h
		}
	}
	
	# Email for Let's Encrypt (set via environment variable)
	email {env.ACME_EMAIL}
}

# HTTP to HTTPS redirect
{env.SERVER_NAME}:80 {
	# Redirect all HTTP traffic to HTTPS
	redir https://{host}{uri} permanent
}

# Main HTTPS server
{env.SERVER_NAME} {
	# Set document root to Laravel's public directory
	root * /app/public
	
	# Enable compression for production
	encode zstd br gzip
	
	# Production security headers
	header {
		# Security headers
		X-Frame-Options "SAMEORIGIN"
		X-XSS-Protection "1; mode=block"
		X-Content-Type-Options "nosniff"
		Referrer-Policy "strict-origin-when-cross-origin"
		Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;"
		
		# HSTS (HTTP Strict Transport Security)
		Strict-Transport-Security "max-age=31536000; includeSubDomains; preload"
		
		# Additional security headers
		X-Permitted-Cross-Domain-Policies "none"
		Cross-Origin-Embedder-Policy "require-corp"
		Cross-Origin-Opener-Policy "same-origin"
		Cross-Origin-Resource-Policy "same-origin"
		
		# Remove server information
		-Server
		-X-Powered-By
	}
	
	# Handle Laravel routes and PHP files
	php_server {
		# Try files in order: exact path, directory with index.php, fallback to index.php
		try_files {path} {path}/index.php index.php
		# Split path on .php for proper PATH_INFO handling
		split_path .php
	}
	
	# Static file caching for production (longer cache times)
	@static {
		file
		path *.css *.js *.png *.jpg *.jpeg *.gif *.ico *.svg *.woff *.woff2 *.ttf *.eot *.map
	}
	header @static {
		Cache-Control "public, max-age=31536000, immutable"
		Expires "1 year"
	}
	
	# Health check endpoint
	respond /health "healthy" 200
	respond /up "up" 200
	
	# Laravel storage symlink handling
	@storage {
		path /storage/*
	}
	rewrite @storage /storage{path}
	
	# Rate limiting for sensitive endpoints
	@login {
		path /login /auth/* /password/*
	}
	rate_limit @login {
		zone login
		key {remote_addr}
		events 10
		window 1m
	}
	
	# Deny access to sensitive files
	@sensitive {
		path /.env* /.git* /composer.* /package.* /webpack.* /.docker* /Dockerfile* /docker-compose.* /storage/logs/* /storage/framework/*
	}
	respond @sensitive 404
	
	# Production access logging
	log {
		output file /var/log/caddy/access.log {
			roll_size 100mb
			roll_keep 10
			roll_keep_for 2160h
		}
		format json
		level INFO
	}
}
