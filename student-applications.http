### Health
GET http://localhost:8000/api/health
Content-Type: application/json

### Login
POST http://localhost:8000/api/v1/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "123456"
}

### Get Student Applications
GET http://localhost:8000/api/student-applications
Content-Type: application/json
Authorization: {{TOKEN}}

### Create Student Applications
POST http://localhost:8000/api/student-applications
Content-Type: application/json
Authorization: {{TOKEN}}

{
    "students": [
        {
            "full_name": "<PERSON>",
            "gender": "male",
            "ethnicity": "Asian",
            "birth_day": 15,
            "birth_month": 6,
            "birth_year": 2000,
            "national_id": "**********12",
            "phone": "+84901234567",
            "email": "<EMAIL>",
            "address": "123 Main Street, Ho Chi Minh City",
            "health_information": "No known allergies",
            "parent_phone": "+84987654321",
            "parent_email": "<EMAIL>",
            "campus_code": "HCM",
            "intended_program": "AI",
            "intended_specialization": null,
            "intake": "AI-FALL-2025",
            "exam_date": "2024-08-15",
            "english_test_type": "IELTS",
            "listening": 7.5,
            "reading": 8.0,
            "writing": 7.0,
            "speaking": 7.5,
            "overall": 7.5,
            "submitted_photo": "https://storage.example.com/documents/john-doe-photo.jpg",
            "submitted_cccd": "https://storage.example.com/documents/john-doe-cccd.pdf",
            "submitted_ccta": null,
            "submitted_tn_translate": "https://storage.example.com/documents/john-doe-transcript-translated.pdf",
            "submitted_hb_translate": "https://storage.example.com/documents/john-doe-diploma-translated.pdf",
            "submitted_other": null,
            "submitted_insurance_card": "https://storage.example.com/documents/john-doe-insurance.pdf",
            "submitted_exemption_gc": null,
            "study_link_status": "pending",
            "english_qualifications": "IELTS Academic",
            "sut_id": "SUT2024001",
            "is_international_applicant": false,
            "exception_units": "None"
        },
        {
            "full_name": "Jane Smith",
            "gender": "female",
            "ethnicity": "Kinh",
            "birth_day": 22,
            "birth_month": 3,
            "birth_year": 1999,
            "national_id": "987654321098",
            "phone": "+84912345678",
            "email": "<EMAIL>",
            "address": "456 Oak Avenue, Hanoi",
            "health_information": "Asthma - controlled with medication",
            "parent_phone": "+84976543210",
            "parent_email": "<EMAIL>",
            "campus_code": "HN",
            "intended_program": "AI",
            "intended_specialization": null,
            "intake": "2024-Spring",
            "exam_date": "2024-07-20",
            "english_test_type": "TOEFL",
            "listening": 85,
            "reading": 88,
            "writing": 82,
            "speaking": 86,
            "overall": 85.25,
            "submitted_photo": "https://storage.example.com/documents/jane-smith-photo.jpg",
            "submitted_cccd": "https://storage.example.com/documents/jane-smith-cccd.pdf",
            "submitted_ccta": "https://storage.example.com/documents/jane-smith-ccta.pdf",
            "submitted_tn_translate": "https://storage.example.com/documents/jane-smith-transcript-translated.pdf",
            "submitted_hb_translate": "https://storage.example.com/documents/jane-smith-diploma-translated.pdf",
            "submitted_other": "https://storage.example.com/documents/jane-smith-additional.pdf",
            "submitted_insurance_card": "https://storage.example.com/documents/jane-smith-insurance.pdf",
            "submitted_exemption_gc": null,
            "study_link_status": "approved",
            "english_qualifications": "TOEFL iBT",
            "sut_id": "SUT2024002",
            "is_international_applicant": false,
            "exception_units": "Math exemption due to high school scores"
        },
        {
            "full_name": "Michael Johnson",
            "gender": "male",
            "ethnicity": "American",
            "birth_day": 8,
            "birth_month": 11,
            "birth_year": 2001,
            "national_id": "US123456789",
            "phone": "+**********",
            "email": "<EMAIL>",
            "address": "789 Pine Street, California, USA",
            "health_information": null,
            "parent_phone": "+**********",
            "parent_email": "<EMAIL>",
            "campus_code": "HCM",
            "intended_program": "AI",
            "intended_specialization": null,
            "intake": "AI-FALL-2025",
            "exam_date": "2024-09-01",
            "english_test_type": "Native",
            "listening": null,
            "reading": null,
            "writing": null,
            "speaking": null,
            "overall": null,
            "submitted_photo": "https://storage.example.com/documents/michael-johnson-photo.jpg",
            "submitted_cccd": null,
            "submitted_ccta": "https://storage.example.com/documents/michael-johnson-ccta.pdf",
            "submitted_tn_translate": "https://storage.example.com/documents/michael-johnson-transcript.pdf",
            "submitted_hb_translate": null,
            "submitted_other": "https://storage.example.com/documents/michael-johnson-passport.pdf",
            "submitted_insurance_card": null,
            "submitted_exemption_gc": "https://storage.example.com/documents/michael-johnson-exemption.pdf",
            "study_link_status": "in_review",
            "english_qualifications": "Native Speaker",
            "sut_id": "SUT2024003",
            "is_international_applicant": true,
            "exception_units": "English language requirement waived"
        }
    ]
}

### Update Existing Student Application (using same email)
POST http://localhost:8000/api/student-applications
Content-Type: application/json
Authorization: {{TOKEN}}

{
  "students": [
    {
      "full_name": "John Doe Updated",
      "gender": "male",
      "ethnicity": "Vietnamese",
      "birth_day": 15,
      "birth_month": 6,
      "birth_year": 2000,
      "national_id": "**********12",
      "phone": "+84901234567",
      "email": "<EMAIL>",
      "address": "456 Updated Street, Ho Chi Minh City",
      "health_information": "Updated health info",
      "parent_phone": "+84987654321",
      "parent_email": "<EMAIL>",
      "campus_code": "HCM",
      "intended_program": "AI",
      "intended_specialization":null,
      "intake": "AI-FALL-2025",
      "exam_date": "2024-08-15",
      "english_test_type": "IELTS",
      "listening": 8.0,
      "reading": 8.5,
      "writing": 7.5,
      "speaking": 8.0,
      "overall": 8.0,
      "submitted_photo": "https://storage.example.com/documents/john-doe-photo-updated.jpg",
      "submitted_cccd": "https://storage.example.com/documents/john-doe-cccd-updated.pdf",
      "submitted_ccta": "https://storage.example.com/documents/john-doe-ccta.pdf",
      "submitted_tn_translate": "https://storage.example.com/documents/john-doe-transcript-updated.pdf",
      "submitted_hb_translate": "https://storage.example.com/documents/john-doe-diploma-updated.pdf",
      "submitted_other": "https://storage.example.com/documents/john-doe-additional.pdf",
      "submitted_insurance_card": "https://storage.example.com/documents/john-doe-insurance-updated.pdf",
      "submitted_exemption_gc": null,
      "study_link_status": "approved",
      "english_qualifications": "IELTS Academic",
      "sut_id": "SUT2024001",
      "is_international_applicant": false,
      "exception_units": "None"
    }
  ]
}

### Test Validation Errors
POST http://localhost:8000/api/student-applications
Content-Type: application/json
Authorization: {{TOKEN}}

{
  "students": [
    {
      "full_name": "",
      "gender": "invalid_gender",
      "birth_day": 32,
      "birth_month": 13,
      "birth_year": 2050,
      "email": "invalid-email",
      "listening": 150.00,
      "reading": -5.00,
      "campus_code": ""
    }
  ]
}

### Minimal Required Fields Only
POST http://localhost:8000/api/student-applications
Content-Type: application/json
Authorization: {{TOKEN}}

{
  "students": [
    {
      "full_name": "Minimal Test Student",
      "email": "<EMAIL>",
      "campus_code": "HCM"
    }
  ]
}
